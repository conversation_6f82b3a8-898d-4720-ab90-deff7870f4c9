import { BaseEmail } from "@/components/email-templates/BaseEmail";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { Button, Section, Text } from "@react-email/components";
import { Company } from "@rvhelp/database";
import { emailStyles, emailUtils } from "./shared-styles";

interface TechnicianWarrantyAuthorizationRejectedEmailProps {
	company: Company;
	warrantyRequest: ExtendedWarrantyRequest;
	rvhelpUrl: string;
	updateNotes?: string;
}

export function TechnicianWarrantyAuthorizationRejectedEmail({
	company,
	warrantyRequest,
	rvhelpUrl,
	updateNotes
}: TechnicianWarrantyAuthorizationRejectedEmailProps) {
	const previewText = "Update on your warranty authorization request";
	// Extract last 6 digits of VIN
	const lastSixDigits = warrantyRequest.rv_vin.slice(-6);

	return (
		<BaseEmail previewText={previewText}>
			<Section>
				<Text style={emailStyles.heading}>
					❌ Warranty Authorization Update
				</Text>

				<Text style={emailUtils.greetingText}>
					Dear {warrantyRequest.first_name},
				</Text>

				<Text style={emailStyles.text}>
					We wanted to inform you about an update regarding your warranty
					authorization request for your {company.name}{" "}
					{warrantyRequest.rv_model}.
				</Text>

				<Text style={emailStyles.text}>
					<span style={{ fontWeight: "bold" }}>Status:</span> Your authorization
					request has been reviewed and cannot be approved at this time.
				</Text>

				{/* Service details section */}
				<Text style={emailStyles.subheading}>Request Details:</Text>

				<Section style={emailStyles.messageBox}>
					<Text style={emailStyles.messageTitle}>Service Information</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>Issue:</span>{" "}
						{warrantyRequest.complaint}
					</Text>
					{warrantyRequest.cause && (
						<Text style={emailStyles.messageText}>
							<span style={{ fontWeight: "bold" }}>Cause:</span>{" "}
							{warrantyRequest.cause}
						</Text>
					)}
					{warrantyRequest.correction && (
						<Text style={emailStyles.messageText}>
							<span style={{ fontWeight: "bold" }}>Correction:</span>{" "}
							{warrantyRequest.correction}
						</Text>
					)}
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>Estimated hours:</span>{" "}
						{warrantyRequest.estimated_hours} hours
					</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>{company.name} model:</span>{" "}
						{warrantyRequest.rv_model}
					</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>VIN:</span> {lastSixDigits}
					</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>Request ID:</span>{" "}
						{warrantyRequest.uuid}
					</Text>
				</Section>

				{updateNotes && (
					<Section style={emailStyles.section}>
						<Text style={emailStyles.subheading}>Reason for Rejection:</Text>
						<Text style={emailStyles.text}>{updateNotes}</Text>
					</Section>
				)}

				<Text style={emailStyles.text}>
					<span style={{ fontWeight: "bold" }}>Next Steps:</span> Please contact
					your technician to discuss alternative solutions or modifications to
					the request that may be eligible for warranty coverage.
				</Text>

				<Text style={emailStyles.text}>
					If you have any questions about this decision or need assistance,
					please contact {company.name} directly or work with your technician to
					explore other options.
				</Text>

				<Section style={emailStyles.centered}>
					<Button
						href={`${rvhelpUrl}/provider/jobs/${warrantyRequest.job_id}`}
						style={emailStyles.buttonSecondary}
					>
						VIEW REQUEST DETAILS
					</Button>
				</Section>

				<Text style={emailStyles.smallText}>
					We appreciate your understanding and are here to help find the best
					solution for your RV service needs.
				</Text>
			</Section>
		</BaseEmail>
	);
}
