import { BaseEmail } from "@/components/email-templates/BaseEmail";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { Button, Section, Text } from "@react-email/components";
import { Company } from "@rvhelp/database";
import { emailStyles, emailUtils } from "./shared-styles";

interface TechnicianWarrantyAuthorizationApprovedEmailProps {
	company: Company;
	warrantyRequest: ExtendedWarrantyRequest;
	rvhelpUrl: string;
	approvedHours?: number;
	updateNotes?: string;
}

export function TechnicianWarrantyAuthorizationApprovedEmail({
	company,
	warrantyRequest,
	rvhelpUrl,
	approvedHours,
	updateNotes
}: TechnicianWarrantyAuthorizationApprovedEmailProps) {
	const previewText = "Your warranty authorization request has been approved!";
	// Extract last 6 digits of VIN
	const lastSixDigits = warrantyRequest.rv_vin.slice(-6);

	return (
		<BaseEmail previewText={previewText}>
			<Section>
				<Text style={emailStyles.heading}>
					✅ Your Warranty Authorization Has Been Approved!
				</Text>

				<Text style={emailUtils.greetingText}>
					Dear {warrantyRequest.first_name},
				</Text>

				<Text style={emailStyles.text}>
					<span style={{ fontWeight: "bold" }}>Great news!</span> Your warranty
					authorization request for your {company.name}{" "}
					{warrantyRequest.rv_model} has been approved.
				</Text>

				{/* Service details section */}
				<Text style={emailStyles.subheading}>Authorization Details:</Text>

				<Section style={emailStyles.messageBox}>
					<Text style={emailStyles.messageTitle}>
						Approved Service Information
					</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>Issue:</span>{" "}
						{warrantyRequest.complaint}
					</Text>
					{warrantyRequest.cause && (
						<Text style={emailStyles.messageText}>
							<span style={{ fontWeight: "bold" }}>Cause:</span>{" "}
							{warrantyRequest.cause}
						</Text>
					)}
					{warrantyRequest.correction && (
						<Text style={emailStyles.messageText}>
							<span style={{ fontWeight: "bold" }}>Correction:</span>{" "}
							{warrantyRequest.correction}
						</Text>
					)}
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>Approved hours:</span>{" "}
						{approvedHours || warrantyRequest.approved_hours} hours
					</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>{company.name} model:</span>{" "}
						{warrantyRequest.rv_model}
					</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>VIN:</span> {lastSixDigits}
					</Text>
					<Text style={emailStyles.messageText}>
						<span style={{ fontWeight: "bold" }}>Request ID:</span>{" "}
						{warrantyRequest.uuid}
					</Text>
				</Section>

				{updateNotes && (
					<Section style={emailStyles.section}>
						<Text style={emailStyles.subheading}>Additional Notes:</Text>
						<Text style={emailStyles.text}>{updateNotes}</Text>
					</Section>
				)}

				<Text style={emailStyles.text}>
					<span style={{ fontWeight: "bold" }}>Next Steps:</span> Your
					technician can now proceed with the approved work. The authorized
					hours and scope of work have been confirmed by {company.name}.
				</Text>

				<Text style={emailStyles.text}>
					If you have any questions about this authorization or need to make
					changes, please contact your technician or {company.name} directly.
				</Text>

				<Section style={emailStyles.centered}>
					<Button
						href={`${rvhelpUrl}/provider/jobs/${warrantyRequest.job_id}`}
						style={emailStyles.button}
					>
						VIEW REQUEST DETAILS
					</Button>
				</Section>

				<Text style={emailStyles.smallText}>
					Thank you for choosing {company.name} warranty service. We appreciate
					your business!
				</Text>
			</Section>
		</BaseEmail>
	);
}
